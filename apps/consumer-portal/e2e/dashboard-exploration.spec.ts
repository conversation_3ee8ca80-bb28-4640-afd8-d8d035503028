import { test, expect } from '@playwright/test';

test.describe('Dashboard Exploration', () => {
  test('should explore dashboard after login and take screenshots', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await expect(page).toHaveTitle('Tallied Cardholder Portal - Tallied Internal Program');

    // Click "Login with email" button
    const loginButton = page.locator('text=Login with email').or(
      page.locator('button:has-text("Login with email")').or(
        page.locator('[data-testid="login-with-email"]').or(
          page.locator('button:has-text("Email")')
        )
      )
    );
    
    await expect(loginButton).toBeVisible({ timeout: 10000 });
    await loginButton.click();

    // Wait for navigation to Zitadel login page
    await page.waitForLoadState('networkidle', { timeout: 15000 });
    await expect(page).toHaveTitle('Welcome Back!');

    // Step 1: Enter username/email
    const usernameInput = page.locator('input[name="loginName"]').or(
      page.locator('input[id="loginName"]')
    );
    
    await expect(usernameInput).toBeVisible({ timeout: 10000 });
    await usernameInput.fill('<EMAIL>');

    // Click Next button
    const nextButton = page.locator('button[type="submit"]').or(
      page.locator('button:has-text("Next")')
    );
    
    await expect(nextButton).toBeVisible({ timeout: 5000 });
    await nextButton.click();

    // Step 2: Wait for password page and enter password
    await page.waitForLoadState('networkidle', { timeout: 10000 });
    
    const passwordInput = page.locator('input[type="password"]').or(
      page.locator('input[name="password"]')
    );
    
    await expect(passwordInput).toBeVisible({ timeout: 10000 });
    await passwordInput.fill('10Apples!');

    // Click the final submit button
    const submitButton = page.locator('button[type="submit"]').or(
      page.locator('button:has-text("Sign in")').or(
        page.locator('button:has-text("Login")').or(
          page.locator('button:has-text("Continue")')
        )
      )
    );
    
    await expect(submitButton).toBeVisible({ timeout: 5000 });
    await submitButton.click();

    // Wait for navigation after login
    await page.waitForLoadState('networkidle', { timeout: 15000 });

    // Verify successful login by checking we're no longer on the login page
    await expect(page).not.toHaveURL(/\/login$/);
    
    // Take screenshot of the main dashboard
    await page.screenshot({ path: 'screenshots/01-dashboard-home.png', fullPage: true });
    console.log('Current URL after login:', page.url());
    console.log('Page title after login:', await page.title());

    // Log all visible navigation elements
    const navElements = await page.locator('nav a, [role="navigation"] a, [data-testid*="nav"], button:has-text("Home"), button:has-text("Payments"), button:has-text("Rewards")').all();
    console.log('Found navigation elements:', navElements.length);
    
    for (let i = 0; i < navElements.length; i++) {
      const element = navElements[i];
      const text = await element.textContent();
      const href = await element.getAttribute('href');
      const isVisible = await element.isVisible();
      console.log(`Nav ${i}: text="${text}", href="${href}", visible=${isVisible}`);
    }

    // Look for main content areas
    const mainContent = await page.locator('main, [role="main"], .dashboard, .content').all();
    console.log('Found main content areas:', mainContent.length);

    // Look for any tabs or tab-like elements
    const tabs = await page.locator('[role="tab"], .tab, button:has-text("Statements"), button:has-text("Activity"), button:has-text("Overview")').all();
    console.log('Found tab elements:', tabs.length);
    
    for (let i = 0; i < tabs.length; i++) {
      const tab = tabs[i];
      const text = await tab.textContent();
      const isVisible = await tab.isVisible();
      console.log(`Tab ${i}: text="${text}", visible=${isVisible}`);
    }

    // Look for any PDF links or download buttons
    const pdfElements = await page.locator('a[href*=".pdf"], button:has-text("Download"), a:has-text("Statement"), a:has-text("PDF")').all();
    console.log('Found PDF/download elements:', pdfElements.length);
    
    for (let i = 0; i < pdfElements.length; i++) {
      const element = pdfElements[i];
      const text = await element.textContent();
      const href = await element.getAttribute('href');
      const isVisible = await element.isVisible();
      console.log(`PDF ${i}: text="${text}", href="${href}", visible=${isVisible}`);
    }

    // Try to find and click on different navigation items if they exist
    const homeNav = page.locator('a:has-text("Home"), button:has-text("Home"), [data-testid*="home"]').first();
    if (await homeNav.isVisible()) {
      console.log('Found Home navigation');
      await homeNav.click();
      await page.waitForLoadState('networkidle', { timeout: 5000 });
      await page.screenshot({ path: 'screenshots/02-home-page.png', fullPage: true });
    }

    const paymentsNav = page.locator('a:has-text("Payments"), button:has-text("Payments"), [data-testid*="payment"]').first();
    if (await paymentsNav.isVisible()) {
      console.log('Found Payments navigation');
      await paymentsNav.click();
      await page.waitForLoadState('networkidle', { timeout: 5000 });
      await page.screenshot({ path: 'screenshots/03-payments-page.png', fullPage: true });
    }

    const rewardsNav = page.locator('a:has-text("Rewards"), button:has-text("Rewards"), [data-testid*="reward"]').first();
    if (await rewardsNav.isVisible()) {
      console.log('Found Rewards navigation');
      await rewardsNav.click();
      await page.waitForLoadState('networkidle', { timeout: 5000 });
      await page.screenshot({ path: 'screenshots/04-rewards-page.png', fullPage: true });
    }

    // Go back to home/dashboard to test tabs
    await page.goto(page.url().split('?')[0]); // Remove any query params
    await page.waitForLoadState('networkidle', { timeout: 5000 });

    // Try to find and test statement tabs
    const statementsTab = page.locator('button:has-text("Statements"), [role="tab"]:has-text("Statements"), .tab:has-text("Statements")').first();
    if (await statementsTab.isVisible()) {
      console.log('Found Statements tab');
      await statementsTab.click();
      await page.waitForLoadState('networkidle', { timeout: 5000 });
      await page.screenshot({ path: 'screenshots/05-statements-tab.png', fullPage: true });
    }
  });
});
