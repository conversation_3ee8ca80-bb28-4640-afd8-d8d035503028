# E2E Tests for Consumer Portal

This directory contains end-to-end (e2e) tests for the Tallied Consumer Portal using Playwright.

## Overview

The e2e tests are designed to run against the deployed preprod environment at:
`https://consumer-portal.preprod.tallied.io`

## Test Structure

### `login.spec.ts`
Contains regression tests for the login functionality:

- **Login Flow Test**: Tests the complete login process including:
  - Navigation to login page
  - Clicking "Login with email" 
  - Entering credentials on Zitadel OAuth provider
  - Successful authentication and redirect

- **Page Display Test**: Verifies the login page loads correctly

## Running Tests

### Prerequisites
Make sure Playwright is installed and browsers are downloaded:
```bash
npm install
npx playwright install
```

### Run Commands

```bash
# Run all e2e tests (headless, all browsers)
npm run test:e2e

# Run tests with browser UI visible
npm run test:e2e:headed

# Run tests with Playwright UI for debugging
npm run test:e2e:ui

# Run tests for specific browser
npx playwright test --project=chromium
npx playwright test --project=firefox  
npx playwright test --project=webkit
```

## Test Credentials

The tests use the following test account:
- **Email**: `<EMAIL>`
- **Password**: `10Apples!`

## Configuration

The Playwright configuration is in `playwright.config.ts` and includes:
- Base URL pointing to preprod environment
- Multiple browser configurations
- Screenshot and video capture on failures
- HTML reporting

## Authentication Flow

The tests handle the OAuth flow with Zitadel:
1. Click "Login with email" on the portal
2. Redirect to Zitadel login page
3. Enter username/email (first step)
4. Click "Next" 
5. Enter password (second step)
6. Submit and redirect back to portal

## Debugging

- Test results and artifacts are saved in `test-results/`
- Screenshots and videos are captured on failures
- Use `--headed` flag to see browser during test execution
- Use `--ui` flag for interactive debugging with Playwright UI
